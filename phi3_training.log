2025-07-06 09:18:47,567 - INFO - MLX-LM LoRA training module is available
2025-07-06 09:19:01,331 - INFO - Preparing dataset only...
2025-07-06 09:19:01,332 - INFO - Processed 100 training examples
2025-07-06 09:19:01,333 - INFO - Training data: 80 examples -> ./data/train.jsonl
2025-07-06 09:19:01,333 - INFO - Validation data: 20 examples -> ./data/valid.jsonl
2025-07-06 09:19:01,333 - INFO - Dataset preparation completed!
2025-07-06 09:19:01,333 - INFO - Next step: python /Users/<USER>/src/modeltrainer/pg_fulltext/phi3_mlx_training.py --data_file postgres_training_data.json
2025-07-06 09:19:33,306 - INFO - MLX-LM LoRA training module is available
2025-07-06 09:19:33,307 - INFO - Preparing dataset...
2025-07-06 09:19:33,307 - INFO - Processed 100 training examples
2025-07-06 09:19:33,308 - INFO - Training data: 80 examples -> ./data/train.jsonl
2025-07-06 09:19:33,308 - INFO - Validation data: 20 examples -> ./data/valid.jsonl
2025-07-06 09:19:33,308 - INFO - Starting training...
2025-07-06 09:19:33,308 - INFO - Starting training with MLX-LM...
2025-07-06 09:19:33,308 - INFO - Command: /Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python -m mlx_lm.lora --model microsoft/Phi-3-mini-4k-instruct --train --data ./data/train.jsonl --iters 1000 --learning-rate 1e-05 --batch-size 4 --lora-layers 16 --adapter-path ./lora_adapters --val-data ./data/valid.jsonl
2025-07-06 09:19:33,308 - INFO - Model: microsoft/Phi-3-mini-4k-instruct
2025-07-06 09:19:33,308 - INFO - Training file: ./data/train.jsonl
2025-07-06 09:19:33,308 - INFO - Validation file: ./data/valid.jsonl
2025-07-06 09:19:33,308 - INFO - Output directory: ./lora_adapters
2025-07-06 09:19:33,308 - INFO - Training iterations: 1000
2025-07-06 09:19:33,308 - INFO - Learning rate: 1e-05
2025-07-06 09:19:33,308 - INFO - Batch size: 4
2025-07-06 09:19:33,308 - INFO - LoRA layers: 16
2025-07-06 09:19:33,928 - ERROR - Training failed with return code 2
2025-07-06 09:19:33,928 - ERROR - Error output:
usage: lora.py [-h] [--model MODEL] [--train] [--data DATA]
               [--fine-tune-type {lora,dora,full}] [--optimizer {adam,adamw}]
               [--mask-prompt] [--num-layers NUM_LAYERS]
               [--batch-size BATCH_SIZE] [--iters ITERS]
               [--val-batches VAL_BATCHES] [--learning-rate LEARNING_RATE]
               [--steps-per-report STEPS_PER_REPORT]
               [--steps-per-eval STEPS_PER_EVAL]
               [--resume-adapter-file RESUME_ADAPTER_FILE]
               [--adapter-path ADAPTER_PATH] [--save-every SAVE_EVERY]
               [--test] [--test-batches TEST_BATCHES]
               [--max-seq-length MAX_SEQ_LENGTH] [-c CONFIG]
               [--grad-checkpoint] [--wandb WANDB] [--seed SEED]
lora.py: error: unrecognized arguments: --lora-layers 16 --val-data ./data/valid.jsonl

2025-07-06 09:19:33,928 - ERROR - Standard output:
Calling `python -m mlx_lm.lora...` directly is deprecated. Use `mlx_lm.lora...` or `python -m mlx_lm lora ...` instead.

2025-07-06 09:19:33,928 - ERROR - Training failed: Command '['/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python', '-m', 'mlx_lm.lora', '--model', 'microsoft/Phi-3-mini-4k-instruct', '--train', '--data', './data/train.jsonl', '--iters', '1000', '--learning-rate', '1e-05', '--batch-size', '4', '--lora-layers', '16', '--adapter-path', './lora_adapters', '--val-data', './data/valid.jsonl']' returned non-zero exit status 2.
2025-07-06 09:39:39,191 - INFO - MLX-LM LoRA training module is available
2025-07-06 09:39:39,192 - INFO - Preparing dataset...
2025-07-06 09:39:39,192 - INFO - Processed 100 training examples
2025-07-06 09:39:39,193 - INFO - Training data: 80 examples -> ./data/train.jsonl
2025-07-06 09:39:39,193 - INFO - Validation data: 20 examples -> ./data/valid.jsonl
2025-07-06 09:39:39,193 - INFO - Starting training...
2025-07-06 09:39:39,193 - INFO - Starting training with MLX-LM...
2025-07-06 09:39:39,193 - INFO - Command: /Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python -m mlx_lm lora --model microsoft/Phi-3-mini-4k-instruct --train --data ./data/train.jsonl --iters 1000 --learning-rate 1e-05 --batch-size 4 --num-layers 16 --adapter-path ./lora_adapters --fine-tune-type lora --steps-per-report 10 --steps-per-eval 100 --save-every 200
2025-07-06 09:39:39,193 - INFO - Model: microsoft/Phi-3-mini-4k-instruct
2025-07-06 09:39:39,193 - INFO - Training file: ./data/train.jsonl
2025-07-06 09:39:39,193 - INFO - Output directory: ./lora_adapters
2025-07-06 09:39:39,193 - INFO - Training iterations: 1000
2025-07-06 09:39:39,193 - INFO - Learning rate: 1e-05
2025-07-06 09:39:39,193 - INFO - Batch size: 4
2025-07-06 09:39:39,193 - INFO - LoRA layers: 16
2025-07-06 10:18:46,637 - INFO - MLX-LM LoRA training module is available
2025-07-06 10:18:46,637 - INFO - Preparing dataset...
2025-07-06 10:18:46,637 - INFO - Processed 100 training examples
2025-07-06 10:18:46,638 - INFO - Training data: 80 examples -> ./data/train.jsonl
2025-07-06 10:18:46,638 - INFO - Validation data: 20 examples -> ./data/valid.jsonl
2025-07-06 10:18:46,638 - INFO - Starting training...
2025-07-06 10:18:46,638 - INFO - Starting training with MLX-LM...
2025-07-06 10:18:46,638 - INFO - Command: /Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python -m mlx_lm lora --model microsoft/Phi-3-mini-4k-instruct --train --data ./data/train.jsonl --iters 1000 --learning-rate 1e-05 --batch-size 4 --num-layers 16 --adapter-path ./lora_adapters --fine-tune-type lora --steps-per-report 10 --steps-per-eval 100 --save-every 200
2025-07-06 10:18:46,638 - INFO - Model: microsoft/Phi-3-mini-4k-instruct
2025-07-06 10:18:46,638 - INFO - Training file: ./data/train.jsonl
2025-07-06 10:18:46,638 - INFO - Output directory: ./lora_adapters
2025-07-06 10:18:46,639 - INFO - Training iterations: 1000
2025-07-06 10:18:46,639 - INFO - Learning rate: 1e-05
2025-07-06 10:18:46,639 - INFO - Batch size: 4
2025-07-06 10:18:46,639 - INFO - LoRA layers: 16
2025-07-06 10:18:47,250 - ERROR - Training failed with return code 1
2025-07-06 10:18:47,250 - ERROR - Error output:
ERROR:root:No safetensors found in /Users/<USER>/.cache/huggingface/hub/models--microsoft--Phi-3-mini-4k-instruct/snapshots/0a67737cc96d2554230f90338b163bc6380a2a85
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/__main__.py", line 28, in <module>
    submodule.main()
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 341, in main
    run(types.SimpleNamespace(**args))
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 301, in run
    model, tokenizer = load(args.model)
                       ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/utils.py", line 241, in load
    model, config = load_model(model_path, lazy)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/utils.py", line 170, in load_model
    raise FileNotFoundError(f"No safetensors found in {model_path}")
FileNotFoundError: No safetensors found in /Users/<USER>/.cache/huggingface/hub/models--microsoft--Phi-3-mini-4k-instruct/snapshots/0a67737cc96d2554230f90338b163bc6380a2a85

2025-07-06 10:18:47,250 - ERROR - Standard output:
Loading pretrained model

2025-07-06 10:18:47,250 - ERROR - Training failed: Command '['/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python', '-m', 'mlx_lm', 'lora', '--model', 'microsoft/Phi-3-mini-4k-instruct', '--train', '--data', './data/train.jsonl', '--iters', '1000', '--learning-rate', '1e-05', '--batch-size', '4', '--num-layers', '16', '--adapter-path', './lora_adapters', '--fine-tune-type', 'lora', '--steps-per-report', '10', '--steps-per-eval', '100', '--save-every', '200']' returned non-zero exit status 1.
2025-07-06 10:40:28,442 - INFO - MLX-LM LoRA training module is available
2025-07-06 10:40:28,442 - INFO - Preparing dataset...
2025-07-06 10:40:28,443 - INFO - Processed 100 training examples
2025-07-06 10:40:28,444 - INFO - Training data: 80 examples -> ./data/train.jsonl
2025-07-06 10:40:28,444 - INFO - Validation data: 20 examples -> ./data/valid.jsonl
2025-07-06 10:40:28,444 - INFO - Starting training...
2025-07-06 10:40:28,444 - INFO - Starting training with MLX-LM...
2025-07-06 10:40:28,444 - INFO - Command: /Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python -m mlx_lm lora --model microsoft/Phi-3-mini-4k-instruct --train --data ./data/train.jsonl --iters 1000 --learning-rate 1e-05 --batch-size 4 --num-layers 16 --adapter-path ./lora_adapters --fine-tune-type lora --steps-per-report 10 --steps-per-eval 100 --save-every 200
2025-07-06 10:40:28,444 - INFO - Model: microsoft/Phi-3-mini-4k-instruct
2025-07-06 10:40:28,444 - INFO - Training file: ./data/train.jsonl
2025-07-06 10:40:28,444 - INFO - Output directory: ./lora_adapters
2025-07-06 10:40:28,444 - INFO - Training iterations: 1000
2025-07-06 10:40:28,444 - INFO - Learning rate: 1e-05
2025-07-06 10:40:28,444 - INFO - Batch size: 4
2025-07-06 10:40:28,444 - INFO - LoRA layers: 16
2025-07-06 10:40:42,654 - ERROR - Training failed with return code 1
2025-07-06 10:40:42,659 - ERROR - Error output:

Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files:  38%|███▊      | 5/13 [00:11<00:18,  2.33s/it]
Fetching 13 files: 100%|██████████| 13/13 [00:11<00:00,  1.11it/s]
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/__main__.py", line 28, in <module>
    submodule.main()
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 341, in main
    run(types.SimpleNamespace(**args))
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 304, in run
    train_set, valid_set, test_set = load_dataset(args, tokenizer)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/tuner/datasets.py", line 305, in load_dataset
    raise ValueError(
ValueError: Training set not found or empty. Must provide training set for fine-tuning.

2025-07-06 10:40:42,659 - ERROR - Standard output:
Loading pretrained model
Loading datasets

2025-07-06 10:40:42,659 - ERROR - Training failed: Command '['/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python', '-m', 'mlx_lm', 'lora', '--model', 'microsoft/Phi-3-mini-4k-instruct', '--train', '--data', './data/train.jsonl', '--iters', '1000', '--learning-rate', '1e-05', '--batch-size', '4', '--num-layers', '16', '--adapter-path', './lora_adapters', '--fine-tune-type', 'lora', '--steps-per-report', '10', '--steps-per-eval', '100', '--save-every', '200']' returned non-zero exit status 1.
2025-07-06 10:41:27,662 - INFO - MLX-LM LoRA training module is available
2025-07-06 10:41:27,662 - INFO - Preparing dataset...
2025-07-06 10:41:27,663 - INFO - Processed 100 training examples
2025-07-06 10:41:27,666 - INFO - Training data: 80 examples -> ./data/train.jsonl
2025-07-06 10:41:27,666 - INFO - Validation data: 20 examples -> ./data/valid.jsonl
2025-07-06 10:41:27,666 - INFO - Starting training...
2025-07-06 10:41:27,666 - INFO - Starting training with MLX-LM...
2025-07-06 10:41:27,666 - INFO - Command: /Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python -m mlx_lm lora --model microsoft/Phi-3-mini-4k-instruct --train --data ./data/train.jsonl --iters 1000 --learning-rate 1e-05 --batch-size 4 --num-layers 16 --adapter-path ./lora_adapters --fine-tune-type lora --steps-per-report 10 --steps-per-eval 100 --save-every 200
2025-07-06 10:41:27,666 - INFO - Model: microsoft/Phi-3-mini-4k-instruct
2025-07-06 10:41:27,666 - INFO - Training file: ./data/train.jsonl
2025-07-06 10:41:27,666 - INFO - Output directory: ./lora_adapters
2025-07-06 10:41:27,666 - INFO - Training iterations: 1000
2025-07-06 10:41:27,666 - INFO - Learning rate: 1e-05
2025-07-06 10:41:27,666 - INFO - Batch size: 4
2025-07-06 10:41:27,666 - INFO - LoRA layers: 16
2025-07-06 10:41:29,217 - ERROR - Training failed with return code 1
2025-07-06 10:41:29,217 - ERROR - Error output:

Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|██████████| 13/13 [00:00<00:00, 61611.25it/s]
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/__main__.py", line 28, in <module>
    submodule.main()
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 341, in main
    run(types.SimpleNamespace(**args))
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 304, in run
    train_set, valid_set, test_set = load_dataset(args, tokenizer)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/tuner/datasets.py", line 305, in load_dataset
    raise ValueError(
ValueError: Training set not found or empty. Must provide training set for fine-tuning.

2025-07-06 10:41:29,217 - ERROR - Standard output:
Loading pretrained model
Loading datasets

2025-07-06 10:41:29,217 - ERROR - Training failed: Command '['/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python', '-m', 'mlx_lm', 'lora', '--model', 'microsoft/Phi-3-mini-4k-instruct', '--train', '--data', './data/train.jsonl', '--iters', '1000', '--learning-rate', '1e-05', '--batch-size', '4', '--num-layers', '16', '--adapter-path', './lora_adapters', '--fine-tune-type', 'lora', '--steps-per-report', '10', '--steps-per-eval', '100', '--save-every', '200']' returned non-zero exit status 1.
2025-07-06 11:12:04,471 - INFO - Testing the trained model...
2025-07-06 11:12:04,471 - INFO - Loading model from microsoft/Phi-3-mini-4k-instruct
2025-07-06 11:12:04,471 - INFO - Loading LoRA adapters from ./lora_adapters
2025-07-06 11:12:14,725 - INFO - Model loaded successfully!
2025-07-06 11:12:14,726 - INFO - Sample generations:
2025-07-06 11:12:14,726 - INFO - ================================================================================
2025-07-06 11:12:14,726 - INFO - 
Test 1:
2025-07-06 11:12:14,726 - INFO - Input:  What is the sensitivity of troponin for myocardial infarction?
2025-07-06 11:12:14,726 - ERROR - ✗ Generation failed: generate_step() got an unexpected keyword argument 'temp'
2025-07-06 11:12:14,726 - INFO - ------------------------------------------------------------
2025-07-06 11:12:14,726 - INFO - 
Test 2:
2025-07-06 11:12:14,726 - INFO - Input:  What are the contraindications for lumbar puncture?
2025-07-06 11:12:14,727 - ERROR - ✗ Generation failed: generate_step() got an unexpected keyword argument 'temp'
2025-07-06 11:12:14,727 - INFO - ------------------------------------------------------------
2025-07-06 11:12:14,727 - INFO - 
Test 3:
2025-07-06 11:12:14,727 - INFO - Input:  What is the treatment for septic shock?
2025-07-06 11:12:14,727 - ERROR - ✗ Generation failed: generate_step() got an unexpected keyword argument 'temp'
2025-07-06 11:12:14,727 - INFO - ------------------------------------------------------------
2025-07-06 11:12:14,727 - INFO - 
Test 4:
2025-07-06 11:12:14,727 - INFO - Input:  What are the risk factors for pulmonary embolism?
2025-07-06 11:12:14,727 - ERROR - ✗ Generation failed: generate_step() got an unexpected keyword argument 'temp'
2025-07-06 11:12:14,727 - INFO - ------------------------------------------------------------
2025-07-06 11:12:14,727 - INFO - 
Test 5:
2025-07-06 11:12:14,727 - INFO - Input:  What is the Glasgow Coma Scale scoring system?
2025-07-06 11:12:14,727 - ERROR - ✗ Generation failed: generate_step() got an unexpected keyword argument 'temp'
2025-07-06 11:12:14,727 - INFO - ------------------------------------------------------------
2025-07-06 11:16:35,924 - INFO - Loading model from microsoft/Phi-3-mini-4k-instruct
2025-07-06 11:16:35,924 - INFO - Loading LoRA adapters from ./lora_adapters
2025-07-06 11:16:46,220 - INFO - Model loaded successfully!
2025-07-06 11:17:40,028 - INFO - Loading model from microsoft/Phi-3-mini-4k-instruct
2025-07-06 11:17:40,028 - INFO - Loading LoRA adapters from ./lora_adapters
2025-07-06 11:18:01,828 - INFO - Model loaded successfully!
2025-07-06 11:18:39,505 - INFO - Testing the trained model...
2025-07-06 11:18:39,505 - INFO - Loading model from microsoft/Phi-3-mini-4k-instruct
2025-07-06 11:18:39,505 - INFO - Loading LoRA adapters from ./lora_adapters
2025-07-06 11:18:59,539 - INFO - Model loaded successfully!
2025-07-06 11:18:59,539 - INFO - Sample generations:
2025-07-06 11:18:59,539 - INFO - ================================================================================
2025-07-06 11:18:59,539 - INFO - 
Test 1:
2025-07-06 11:18:59,539 - INFO - Input:  What is the sensitivity of troponin for myocardial infarction?
2025-07-06 11:19:00,693 - INFO - Output: 
2025-07-06 11:19:00,694 - INFO - ⚠ No logical operators detected
2025-07-06 11:19:00,694 - INFO - ------------------------------------------------------------
2025-07-06 11:19:00,694 - INFO - 
Test 2:
2025-07-06 11:19:00,694 - INFO - Input:  What are the contraindications for lumbar puncture?
2025-07-06 11:19:01,228 - INFO - Output: 
2025-07-06 11:19:01,229 - INFO - ⚠ No logical operators detected
2025-07-06 11:19:01,229 - INFO - ------------------------------------------------------------
2025-07-06 11:19:01,229 - INFO - 
Test 3:
2025-07-06 11:19:01,229 - INFO - Input:  What is the treatment for septic shock?
2025-07-06 11:19:01,542 - INFO - Output: 
2025-07-06 11:19:01,542 - INFO - ⚠ No logical operators detected
2025-07-06 11:19:01,542 - INFO - ------------------------------------------------------------
2025-07-06 11:19:01,543 - INFO - 
Test 4:
2025-07-06 11:19:01,543 - INFO - Input:  What are the risk factors for pulmonary embolism?
2025-07-06 11:19:02,101 - INFO - Output: 
2025-07-06 11:19:02,101 - INFO - ⚠ No logical operators detected
2025-07-06 11:19:02,101 - INFO - ------------------------------------------------------------
2025-07-06 11:19:02,101 - INFO - 
Test 5:
2025-07-06 11:19:02,101 - INFO - Input:  What is the Glasgow Coma Scale scoring system?
2025-07-06 11:19:04,460 - INFO - Output: 
2025-07-06 11:19:04,469 - INFO - ⚠ No logical operators detected
2025-07-06 11:19:04,469 - INFO - ------------------------------------------------------------
2025-07-06 11:23:13,842 - INFO - Testing the trained model...
2025-07-06 11:23:13,842 - INFO - Loading model from microsoft/Phi-3-mini-4k-instruct
2025-07-06 11:23:13,842 - INFO - Loading LoRA adapters from ./lora_adapters
2025-07-06 11:23:16,275 - INFO - Model loaded successfully!
2025-07-06 11:23:16,275 - INFO - Sample generations:
2025-07-06 11:23:16,275 - INFO - ================================================================================
2025-07-06 11:23:16,275 - INFO - 
Test 1:
2025-07-06 11:23:16,275 - INFO - Input:  What is the sensitivity of troponin for myocardial infarction?
2025-07-06 11:23:17,084 - INFO - Output: sensitivity & troponin & (MI | "myocardial infarction")
2025-07-06 11:23:17,084 - INFO - ✓ Contains logical operators
2025-07-06 11:23:17,084 - INFO - ------------------------------------------------------------
2025-07-06 11:23:17,084 - INFO - 
Test 2:
2025-07-06 11:23:17,084 - INFO - Input:  What are the contraindications for lumbar puncture?
2025-07-06 11:23:17,622 - INFO - Output: contraindication & ("lumbar puncture" | LP)
2025-07-06 11:23:17,622 - INFO - ✓ Contains logical operators
2025-07-06 11:23:17,622 - INFO - ------------------------------------------------------------
2025-07-06 11:23:17,622 - INFO - 
Test 3:
2025-07-06 11:23:17,622 - INFO - Input:  What is the treatment for septic shock?
2025-07-06 11:23:17,946 - INFO - Output: treatment & septic & shock
2025-07-06 11:23:17,946 - INFO - ✓ Contains logical operators
2025-07-06 11:23:17,946 - INFO - ------------------------------------------------------------
2025-07-06 11:23:17,946 - INFO - 
Test 4:
2025-07-06 11:23:17,946 - INFO - Input:  What are the risk factors for pulmonary embolism?
2025-07-06 11:23:18,507 - INFO - Output: risk & factor & ts.pe & ts.thromboembolism
2025-07-06 11:23:18,519 - INFO - ✓ Contains logical operators
2025-07-06 11:23:18,519 - INFO - ------------------------------------------------------------
2025-07-06 11:23:18,519 - INFO - 
Test 5:
2025-07-06 11:23:18,519 - INFO - Input:  What is the Glasgow Coma Scale scoring system?
2025-07-06 11:23:20,880 - INFO - Output: Glasgow & (Coma & Scale) & scoring<|user|> tsquery
2025-07-06 11:23:20,881 - INFO - ✓ Contains logical operators
2025-07-06 11:23:20,881 - INFO - ------------------------------------------------------------
2025-07-06 15:00:38,089 - INFO - Loading model from microsoft/Phi-3-mini-4k-instruct
2025-07-06 15:00:38,089 - INFO - Loading LoRA adapters from ./lora_adapters
2025-07-06 15:00:40,489 - INFO - Model loaded successfully!
2025-07-06 15:10:16,082 - INFO - MLX-LM LoRA training module is available
2025-07-06 15:10:16,082 - INFO - Preparing dataset...
2025-07-06 15:10:16,082 - INFO - Using default JSON file: postgres_training_data.json
2025-07-06 15:10:16,083 - INFO - Loaded 100 examples from postgres_training_data.json
2025-07-06 15:10:16,083 - INFO - Training data: 80 examples -> ./data/train.jsonl
2025-07-06 15:10:16,084 - INFO - Validation data: 20 examples -> ./data/valid.jsonl
2025-07-06 15:10:16,084 - INFO - Starting training...
2025-07-06 15:10:16,084 - INFO - Starting training with MLX-LM...
2025-07-06 15:10:16,084 - INFO - Command: /Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python -m mlx_lm lora --model microsoft/Phi-3-mini-4k-instruct --train --data ./data/train.jsonl --iters 2500 --learning-rate 3e-06 --batch-size 12 --num-layers 32 --adapter-path ./lora_adapters --fine-tune-type lora --steps-per-report 10 --steps-per-eval 100 --save-every 200
2025-07-06 15:10:16,084 - INFO - Model: microsoft/Phi-3-mini-4k-instruct
2025-07-06 15:10:16,084 - INFO - Training file: ./data/train.jsonl
2025-07-06 15:10:16,084 - INFO - Output directory: ./lora_adapters
2025-07-06 15:10:16,084 - INFO - Training iterations: 2500
2025-07-06 15:10:16,084 - INFO - Learning rate: 3e-06
2025-07-06 15:10:16,084 - INFO - Batch size: 12
2025-07-06 15:10:16,084 - INFO - LoRA layers: 32
2025-07-06 15:10:25,918 - ERROR - Training failed with return code 1
2025-07-06 15:10:25,918 - ERROR - Error output:

A module that was compiled using NumPy 1.x cannot be run in
NumPy 2.3.1 as it may crash. To support both 1.x and 2.x
versions of NumPy, modules must be compiled with NumPy 2.0.
Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.

If you are a user of the module, the easiest solution will be to
downgrade to 'numpy<2' or try to upgrade the affected module.
We expect that some modules will need time to support NumPy 2.

Traceback (most recent call last):  File "<frozen runpy>", line 189, in _run_module_as_main
  File "<frozen runpy>", line 148, in _get_module_details
  File "<frozen runpy>", line 112, in _get_module_details
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/__init__.py", line 9, in <module>
    from .convert import convert
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/convert.py", line 11, in <module>
    from .utils import (
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/utils.py", line 37, in <module>
    from transformers import PreTrainedTokenizer
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/__init__.py", line 27, in <module>
    from . import dependency_versions_check
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/dependency_versions_check.py", line 16, in <module>
    from .utils.versions import require_version, require_version_core
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/utils/__init__.py", line 24, in <module>
    from .args_doc import (
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/utils/args_doc.py", line 30, in <module>
    from .generic import ModelOutput
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/utils/generic.py", line 46, in <module>
    import torch  # noqa: F401
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/__init__.py", line 1477, in <module>
    from .functional import *  # noqa: F403
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/functional.py", line 9, in <module>
    import torch.nn.functional as F
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/nn/__init__.py", line 1, in <module>
    from .modules import *  # noqa: F403
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/nn/modules/__init__.py", line 35, in <module>
    from .transformer import TransformerEncoder, TransformerDecoder, \
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/nn/modules/transformer.py", line 20, in <module>
    device: torch.device = torch.device(torch._C._get_default_device()),  # torch.device('cpu'),
/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/nn/modules/transformer.py:20: UserWarning: Failed to initialize NumPy: _ARRAY_API not found (Triggered internally at /Users/<USER>/work/pytorch/pytorch/pytorch/torch/csrc/utils/tensor_numpy.cpp:84.)
  device: torch.device = torch.device(torch._C._get_default_device()),  # torch.device('cpu'),

Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|██████████| 13/13 [00:00<00:00, 68413.99it/s]
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/__main__.py", line 28, in <module>
    submodule.main()
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 341, in main
    run(types.SimpleNamespace(**args))
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 304, in run
    train_set, valid_set, test_set = load_dataset(args, tokenizer)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/tuner/datasets.py", line 305, in load_dataset
    raise ValueError(
ValueError: Training set not found or empty. Must provide training set for fine-tuning.

2025-07-06 15:10:25,918 - ERROR - Standard output:
Loading pretrained model
Loading datasets

2025-07-06 15:10:25,918 - ERROR - Training failed: Command '['/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python', '-m', 'mlx_lm', 'lora', '--model', 'microsoft/Phi-3-mini-4k-instruct', '--train', '--data', './data/train.jsonl', '--iters', '2500', '--learning-rate', '3e-06', '--batch-size', '12', '--num-layers', '32', '--adapter-path', './lora_adapters', '--fine-tune-type', 'lora', '--steps-per-report', '10', '--steps-per-eval', '100', '--save-every', '200']' returned non-zero exit status 1.
2025-07-06 15:14:03,613 - INFO - MLX-LM LoRA training module is available
2025-07-06 15:14:03,613 - INFO - Preparing dataset...
2025-07-06 15:14:03,613 - INFO - Loading data from JSONL file: ./human_verified_training_data/synthetic_deepseekR1.jsonl
2025-07-06 15:14:03,616 - INFO - Loaded 1887 examples from ./human_verified_training_data/synthetic_deepseekR1.jsonl
2025-07-06 15:14:03,620 - INFO - Training data: 1509 examples -> ./data/train.jsonl
2025-07-06 15:14:03,620 - INFO - Validation data: 378 examples -> ./data/valid.jsonl
2025-07-06 15:14:03,620 - INFO - Starting training...
2025-07-06 15:14:03,620 - INFO - Starting training with MLX-LM...
2025-07-06 15:14:03,620 - INFO - Command: /Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python -m mlx_lm lora --model microsoft/Phi-3-mini-4k-instruct --train --data ./data/train.jsonl --iters 2500 --learning-rate 3e-06 --batch-size 12 --num-layers 32 --adapter-path ./lora_adapters --fine-tune-type lora --steps-per-report 10 --steps-per-eval 100 --save-every 200
2025-07-06 15:14:03,620 - INFO - Model: microsoft/Phi-3-mini-4k-instruct
2025-07-06 15:14:03,620 - INFO - Training file: ./data/train.jsonl
2025-07-06 15:14:03,620 - INFO - Output directory: ./lora_adapters
2025-07-06 15:14:03,620 - INFO - Training iterations: 2500
2025-07-06 15:14:03,620 - INFO - Learning rate: 3e-06
2025-07-06 15:14:03,620 - INFO - Batch size: 12
2025-07-06 15:14:03,620 - INFO - LoRA layers: 32
2025-07-06 15:14:05,977 - ERROR - Training failed with return code 1
2025-07-06 15:14:05,978 - ERROR - Error output:

A module that was compiled using NumPy 1.x cannot be run in
NumPy 2.3.1 as it may crash. To support both 1.x and 2.x
versions of NumPy, modules must be compiled with NumPy 2.0.
Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.

If you are a user of the module, the easiest solution will be to
downgrade to 'numpy<2' or try to upgrade the affected module.
We expect that some modules will need time to support NumPy 2.

Traceback (most recent call last):  File "<frozen runpy>", line 189, in _run_module_as_main
  File "<frozen runpy>", line 148, in _get_module_details
  File "<frozen runpy>", line 112, in _get_module_details
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/__init__.py", line 9, in <module>
    from .convert import convert
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/convert.py", line 11, in <module>
    from .utils import (
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/utils.py", line 37, in <module>
    from transformers import PreTrainedTokenizer
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/__init__.py", line 27, in <module>
    from . import dependency_versions_check
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/dependency_versions_check.py", line 16, in <module>
    from .utils.versions import require_version, require_version_core
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/utils/__init__.py", line 24, in <module>
    from .args_doc import (
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/utils/args_doc.py", line 30, in <module>
    from .generic import ModelOutput
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/transformers/utils/generic.py", line 46, in <module>
    import torch  # noqa: F401
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/__init__.py", line 1477, in <module>
    from .functional import *  # noqa: F403
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/functional.py", line 9, in <module>
    import torch.nn.functional as F
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/nn/__init__.py", line 1, in <module>
    from .modules import *  # noqa: F403
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/nn/modules/__init__.py", line 35, in <module>
    from .transformer import TransformerEncoder, TransformerDecoder, \
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/nn/modules/transformer.py", line 20, in <module>
    device: torch.device = torch.device(torch._C._get_default_device()),  # torch.device('cpu'),
/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/torch/nn/modules/transformer.py:20: UserWarning: Failed to initialize NumPy: _ARRAY_API not found (Triggered internally at /Users/<USER>/work/pytorch/pytorch/pytorch/torch/csrc/utils/tensor_numpy.cpp:84.)
  device: torch.device = torch.device(torch._C._get_default_device()),  # torch.device('cpu'),

Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|██████████| 13/13 [00:00<00:00, 27650.08it/s]
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/__main__.py", line 28, in <module>
    submodule.main()
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 341, in main
    run(types.SimpleNamespace(**args))
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 304, in run
    train_set, valid_set, test_set = load_dataset(args, tokenizer)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/tuner/datasets.py", line 305, in load_dataset
    raise ValueError(
ValueError: Training set not found or empty. Must provide training set for fine-tuning.

2025-07-06 15:14:05,978 - ERROR - Standard output:
Loading pretrained model
Loading datasets

2025-07-06 15:14:05,978 - ERROR - Training failed: Command '['/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python', '-m', 'mlx_lm', 'lora', '--model', 'microsoft/Phi-3-mini-4k-instruct', '--train', '--data', './data/train.jsonl', '--iters', '2500', '--learning-rate', '3e-06', '--batch-size', '12', '--num-layers', '32', '--adapter-path', './lora_adapters', '--fine-tune-type', 'lora', '--steps-per-report', '10', '--steps-per-eval', '100', '--save-every', '200']' returned non-zero exit status 1.
2025-07-06 15:18:02,595 - INFO - MLX-LM LoRA training module is available
2025-07-06 15:18:02,595 - INFO - Preparing dataset...
2025-07-06 15:18:02,595 - INFO - Loading data from JSONL file: ./human_verified_training_data/synthetic_deepseekR1.jsonl
2025-07-06 15:18:02,598 - INFO - Loaded 1887 examples from ./human_verified_training_data/synthetic_deepseekR1.jsonl
2025-07-06 15:18:02,602 - INFO - Training data: 1509 examples -> ./data/train.jsonl
2025-07-06 15:18:02,602 - INFO - Validation data: 378 examples -> ./data/valid.jsonl
2025-07-06 15:18:02,602 - INFO - Starting training...
2025-07-06 15:18:02,602 - INFO - Starting training with MLX-LM...
2025-07-06 15:18:02,602 - INFO - Command: /Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python -m mlx_lm lora --model microsoft/Phi-3-mini-4k-instruct --train --data ./data/train.jsonl --iters 2500 --learning-rate 3e-06 --batch-size 12 --num-layers 32 --adapter-path ./lora_adapters --fine-tune-type lora --steps-per-report 10 --steps-per-eval 100 --save-every 200
2025-07-06 15:18:02,602 - INFO - Model: microsoft/Phi-3-mini-4k-instruct
2025-07-06 15:18:02,602 - INFO - Training file: ./data/train.jsonl
2025-07-06 15:18:02,602 - INFO - Output directory: ./lora_adapters
2025-07-06 15:18:02,602 - INFO - Training iterations: 2500
2025-07-06 15:18:02,602 - INFO - Learning rate: 3e-06
2025-07-06 15:18:02,602 - INFO - Batch size: 12
2025-07-06 15:18:02,602 - INFO - LoRA layers: 32
2025-07-06 15:18:05,725 - ERROR - Training failed with return code 1
2025-07-06 15:18:05,726 - ERROR - Error output:

Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|██████████| 13/13 [00:00<00:00, 47455.14it/s]
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/__main__.py", line 28, in <module>
    submodule.main()
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 341, in main
    run(types.SimpleNamespace(**args))
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/lora.py", line 304, in run
    train_set, valid_set, test_set = load_dataset(args, tokenizer)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/lib/python3.12/site-packages/mlx_lm/tuner/datasets.py", line 305, in load_dataset
    raise ValueError(
ValueError: Training set not found or empty. Must provide training set for fine-tuning.

2025-07-06 15:18:05,727 - ERROR - Standard output:
Loading pretrained model
Loading datasets

2025-07-06 15:18:05,727 - ERROR - Training failed: Command '['/Users/<USER>/src/modeltrainer/pg_fulltext/.venv/bin/python', '-m', 'mlx_lm', 'lora', '--model', 'microsoft/Phi-3-mini-4k-instruct', '--train', '--data', './data/train.jsonl', '--iters', '2500', '--learning-rate', '3e-06', '--batch-size', '12', '--num-layers', '32', '--adapter-path', './lora_adapters', '--fine-tune-type', 'lora', '--steps-per-report', '10', '--steps-per-eval', '100', '--save-every', '200']' returned non-zero exit status 1.
