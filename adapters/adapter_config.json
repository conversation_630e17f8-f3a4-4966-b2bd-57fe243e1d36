{"adapter_path": "adapters", "batch_size": 1, "config": null, "data": "./data", "fine_tune_type": "lora", "grad_checkpoint": false, "iters": 10, "learning_rate": 3e-06, "lora_parameters": {"rank": 8, "dropout": 0.0, "scale": 20.0}, "lr_schedule": null, "mask_prompt": false, "max_seq_length": 2048, "model": "microsoft/Phi-3-mini-4k-instruct", "num_layers": 16, "optimizer": "adam", "optimizer_config": {"adam": {}, "adamw": {}}, "resume_adapter_file": null, "save_every": 100, "seed": 0, "steps_per_eval": 200, "steps_per_report": 10, "test": false, "test_batches": 500, "train": true, "val_batches": 25, "wandb": null}